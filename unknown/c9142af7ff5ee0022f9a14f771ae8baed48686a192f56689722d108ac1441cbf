import { NextResponse } from 'next/server';
import { getEmailService, createSupportTicketNotificationEmail, createSupportTicketConfirmationEmail } from '@/lib/email-service';
import { saveSupportTicket } from '@/lib/supabase';

// Generate unique ticket number
function generateTicketNumber(): string {
  const timestamp = Date.now().toString(36);
  const randomStr = Math.random().toString(36).substring(2, 8);
  return `UZ-${timestamp}-${randomStr}`.toUpperCase();
}

export async function POST(request: Request) {
  try {
    const { name, email, problemDescription, conversationHistory } = await request.json();

    // Basic validation
    if (!name || !email || !problemDescription) {
      return NextResponse.json(
        { error: 'Name, email, and problem description are required' },
        { status: 400 }
      );
    }

    if (!/\S+@\S+\.\S+/.test(email)) {
      return NextResponse.json(
        { error: 'Valid email is required' },
        { status: 400 }
      );
    }

    // Generate unique ticket number
    const ticketNumber = generateTicketNumber();

    // Save to database
    const ticketData = {
      ticket_number: ticketNumber,
      name,
      email,
      problem_description: problemDescription,
      conversation_history: conversationHistory || '',
      status: 'open',
      created_at: new Date().toISOString()
    };

    const saveResult = await saveSupportTicket(ticketData);
    if (saveResult.error) {
      console.error('Failed to save support ticket:', saveResult.error);
      // Continue with email sending even if database save fails
    }

    const emailService = getEmailService();

    // Create notification email for admin team
    const notificationHtml = createSupportTicketNotificationEmail({
      ticketNumber,
      name,
      email,
      problemDescription,
      conversationHistory: conversationHistory || ''
    });

    // Create confirmation email for client
    const confirmationHtml = createSupportTicketConfirmationEmail({
      name,
      ticketNumber
    });

    // Send both emails
    await emailService.sendMultipleEmails([
      {
        to: ['<EMAIL>', '<EMAIL>'],
        subject: `🎫 New Support Ticket #${ticketNumber} - ${name}`,
        html: notificationHtml
      },
      {
        to: email,
        subject: `Support Ticket Created - #${ticketNumber}`,
        html: confirmationHtml
      }
    ]);

    return NextResponse.json({
      message: 'Support ticket created successfully',
      ticketNumber,
      success: true
    });
  } catch (error) {
    console.error('Error creating support ticket:', error);
    return NextResponse.json(
      {
        error: 'Failed to create support ticket',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
