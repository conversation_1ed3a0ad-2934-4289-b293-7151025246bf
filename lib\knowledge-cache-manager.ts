// Server-side only knowledge cache manager
import * as fs from 'fs';
import * as path from 'path';
import { EnhancedKnowledge } from './knowledge-scraper';

export class KnowledgeCacheManager {
  private readonly CACHE_FILE = 'public/knowledge-cache.json';
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours

  // Load knowledge base from cache file
  async loadFromCache(): Promise<EnhancedKnowledge | null> {
    try {
      if (!fs.existsSync(this.CACHE_FILE)) {
        console.log('📄 No cache file found');
        return null;
      }

      const cacheData = fs.readFileSync(this.CACHE_FILE, 'utf-8');
      const cached: EnhancedKnowledge = JSON.parse(cacheData);

      // Check if cache is still valid
      const now = new Date();
      const expiry = new Date(cached.cacheExpiry);
      
      if (now > expiry) {
        console.log('📅 Cache expired, will refresh');
        return null;
      }

      console.log('📦 Loaded knowledge base from cache file');
      return cached;
    } catch (error) {
      console.warn('⚠️ Failed to load cache:', error);
      return null;
    }
  }

  // Save knowledge base to cache file
  async saveToCache(knowledgeBase: EnhancedKnowledge): Promise<boolean> {
    try {
      const cacheDir = path.dirname(this.CACHE_FILE);
      if (!fs.existsSync(cacheDir)) {
        fs.mkdirSync(cacheDir, { recursive: true });
      }

      fs.writeFileSync(this.CACHE_FILE, JSON.stringify(knowledgeBase, null, 2));
      console.log('💾 Knowledge base saved to cache file');
      return true;
    } catch (error) {
      console.warn('⚠️ Failed to save cache:', error);
      return false;
    }
  }

  // Delete cache file
  async clearCache(): Promise<boolean> {
    try {
      if (fs.existsSync(this.CACHE_FILE)) {
        fs.unlinkSync(this.CACHE_FILE);
        console.log('🗑️ Cache file deleted');
        return true;
      }
      return true;
    } catch (error) {
      console.warn('⚠️ Failed to delete cache:', error);
      return false;
    }
  }

  // Check if cache file exists and is valid
  isCacheValid(): boolean {
    try {
      if (!fs.existsSync(this.CACHE_FILE)) {
        return false;
      }

      const cacheData = fs.readFileSync(this.CACHE_FILE, 'utf-8');
      const cached: EnhancedKnowledge = JSON.parse(cacheData);

      const now = new Date();
      const expiry = new Date(cached.cacheExpiry);
      
      return now <= expiry;
    } catch (error) {
      return false;
    }
  }

  // Get cache file stats
  getCacheStats(): { exists: boolean; size?: number; lastModified?: string; isValid?: boolean } {
    try {
      if (!fs.existsSync(this.CACHE_FILE)) {
        return { exists: false };
      }

      const stats = fs.statSync(this.CACHE_FILE);
      return {
        exists: true,
        size: stats.size,
        lastModified: stats.mtime.toISOString(),
        isValid: this.isCacheValid()
      };
    } catch (error) {
      return { exists: false };
    }
  }
}
