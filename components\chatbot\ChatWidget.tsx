'use client';

import { useState, useEffect, useRef } from 'react';
import { saveLead } from '../../lib/supabase';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import LeadForm from './LeadForm';
import ChatInput from './ChatInput';
import { IntentDetector } from './intent-detector';
import { ConversationStates, ConversationState, TIMEOUT_DURATION, WARNING_DURATION, SCREEN_BREAKPOINTS } from './constants';
import { EnhancedAIResponder, createEnhancedResponder, AIResponseContext } from '../../lib/enhanced-ai-responder';
import {
  Message,
  ScreenSize,
  UserContext,
  LeadFormData,
  CompanyKnowledge,
  SupportTicketData
} from './types';

export default function ChatWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [isExpanding, setIsExpanding] = useState(false);
  const [showChatContent, setShowChatContent] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [companyKnowledge, setCompanyKnowledge] = useState<CompanyKnowledge>({});
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [conversationState, setConversationState] = useState<ConversationState>(ConversationStates.WELCOME);
  const [userContext, setUserContext] = useState<UserContext>({
    name: '',
    email: '',
    lastIntent: null,
    selectedService: null,
    awaitingResponse: false
  });
  const [enhancedResponder, setEnhancedResponder] = useState<EnhancedAIResponder | null>(null);
  const [leadForm, setLeadForm] = useState<LeadFormData>({
    name: '',
    email: '',
    isSubmitting: false,
    error: null
  });
  const [showLeadForm, setShowLeadForm] = useState(false);
  const [showCalendly, setShowCalendly] = useState(false);
  const [isConversationEnded, setIsConversationEnded] = useState(false);
  const [lastActivityTime, setLastActivityTime] = useState(Date.now());
  const [timeoutWarningShown, setTimeoutWarningShown] = useState(false);
  const [screenSize, setScreenSize] = useState<ScreenSize>({
    width: 1200,
    height: 800,
    isSmall: false,
  });
  const [isClient, setIsClient] = useState(false);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      setTimeout(() => {
        chatContainerRef.current!.scrollTop = chatContainerRef.current!.scrollHeight;
      }, 100);
    }
  };

  // Handle Kraken-style opening animation
  const handleOpen = () => {
    setIsOpen(true);
    setIsExpanding(true);

    // Start the expansion animation
    setTimeout(() => {
      // Show chat content after the widget has expanded
      setShowChatContent(true);
      setIsExpanding(false);
    }, 400); // Match the animation duration
  };

  // Handle smooth closing animation
  const handleClose = () => {
    setIsClosing(true);
    setShowChatContent(false);

    // Wait for animation to complete before actually closing
    setTimeout(() => {
      setIsOpen(false);
      setIsClosing(false);
    }, 400); // Match the animation duration
  };

  // Conversation Flow Management
  const updateActivityTime = () => {
    setLastActivityTime(Date.now());
    setTimeoutWarningShown(false);
  };

  const transitionToState = (newState: ConversationState, context: Partial<UserContext> = {}) => {
    setConversationState(newState);
    setUserContext(prev => ({ ...prev, ...context }));
    updateActivityTime();
  };

  // Enhanced function to ensure proper message formatting
  const formatMessageForDisplay = (text: string): string => {
    if (!text || typeof text !== 'string') return text;

    // Apply consistent formatting rules to all bot messages
    let formattedText = text;

    // Add line breaks after sentences that end with periods, exclamation marks, or question marks
    // but only if they're followed by a capital letter or "For example" (indicating a new sentence/section)
    formattedText = formattedText.replace(/([.!?])\s+([A-Z]|For example)/g, '$1\n\n$2');

    // Add line breaks before "For example," patterns
    formattedText = formattedText.replace(/\s+(For example,)/gi, '\n\n$1');

    // Add line breaks after price ranges and before new information
    formattedText = formattedText.replace(/(€\d+[,\d]*(?:\s*to\s*€\d+[,\d]*)?[.,]?)\s+([A-Z])/g, '$1\n\n$2');

    // Add line breaks before "After" patterns (like "After a free consultation")
    formattedText = formattedText.replace(/\s+(After\s+)/gi, '\n\n$1');

    // Add spacing around key pricing phrases that should stand out
    formattedText = formattedText.replace(/(landing pages start from|full websites range from|chatbot setups typically cost|chatbots typically cost)/gi, '\n\n$1');

    // Add line breaks before concluding statements
    formattedText = formattedText.replace(/\s+(After a free consultation,|You'll receive)/gi, '\n\n$1');

    return formattedText;
  };

  const addBotMessage = (text: string, type: string | null = null, delay = 800) => {
    setTimeout(() => {
      setMessages(prev => {
        // Format the message for better display
        const formattedText = formatMessageForDisplay(text);

        // Prevent duplicate messages (especially for buttons/types)
        const lastMessage = prev[prev.length - 1];
        if (lastMessage && !lastMessage.isUser && lastMessage.text === formattedText && lastMessage.type === (type || undefined)) {
          console.log('🚫 Prevented duplicate message:', formattedText, type);
          return prev; // Don't add duplicate
        }
        return [...prev, { text: formattedText, isUser: false, type: type || undefined }];
      });
      setIsBotTyping(false);
    }, delay);
  };

  const addBotMessages = (messageArray: (string | { type: string })[], delay = 800) => {
    setTimeout(() => {
      setMessages(prev => [
        ...prev,
        ...messageArray.map(msg =>
          typeof msg === 'string'
            ? { text: formatMessageForDisplay(msg), isUser: false }
            : { ...msg, text: '', isUser: false }
        )
      ]);
      setIsBotTyping(false);

      // Check if calendly widget was added and schedule follow-up message
      const hasCalendlyWidget = messageArray.some(msg =>
        typeof msg === 'object' && msg.type === 'calendly'
      );
      if (hasCalendlyWidget) {
        scheduleCalendlyFollowUp();
      }
    }, delay);
  };

  const scheduleCalendlyFollowUp = () => {
    // Wait 2 seconds after calendly is shown, then ask if user needs anything else
    setTimeout(() => {
      addBotMessage("Let us know if you need anything else! 😊");
      transitionToState(ConversationStates.ANYTHING_ELSE);
    }, 2000);
  };

  // Set up CSS variables based on screen size
  useEffect(() => {
    const root = document.documentElement;
    if (screenSize.isSmall) {
      // Mobile-friendly sizes - different variations based on actual width
      if (screenSize.width <= SCREEN_BREAKPOINTS.EXTRA_SMALL) { // iPhone SE, smaller devices
        root.style.setProperty('--chat-font-size', '13px');
        root.style.setProperty('--chat-padding', '6px');
      } else if (screenSize.width <= SCREEN_BREAKPOINTS.SMALL) { // iPhone X/11/12 mini
        root.style.setProperty('--chat-font-size', '14px');
        root.style.setProperty('--chat-padding', '8px');
      } else { // Other mobile devices
        root.style.setProperty('--chat-font-size', '15px');
        root.style.setProperty('--chat-padding', '10px');
      }
    } else {
      // Reset to default sizes
      root.style.setProperty('--chat-font-size', '16px');
      root.style.setProperty('--chat-padding', '16px');
    }
  }, [screenSize]);

  // Initialize welcome flow when chat opens
  useEffect(() => {
    if (isOpen && conversationState === ConversationStates.WELCOME && messages.length === 0) {
      setIsBotTyping(true);
      // Welcome message with typing delay
      setTimeout(() => {
        setMessages([
          { text: "Hi there! 👋 I'm the UpZera assistant.", isUser: false }
        ]);
        setIsBotTyping(true);

        // Show main menu after another delay
        setTimeout(() => {
          setMessages(prev => [
            ...prev,
            { text: "We build smart digital tools that move businesses forward. What would you like to know about?", isUser: false },
            { type: 'mainMenu', text: '', isUser: false }
          ]);
          setIsBotTyping(false);
          transitionToState(ConversationStates.MAIN_MENU);
        }, 1200);
      }, 800);
    }
  }, [isOpen, conversationState, messages.length]);

  // Timeout and Re-engagement Handling
  useEffect(() => {
    if (!isOpen || isConversationEnded) return;

    const checkInactivity = () => {
      const now = Date.now();
      const timeSinceLastActivity = now - lastActivityTime;

      // Show warning at 4 minutes
      if (timeSinceLastActivity >= WARNING_DURATION && !timeoutWarningShown) {
        setTimeoutWarningShown(true);
        setIsBotTyping(true);
        addBotMessage("Are you still there? I'm here if you need any help! 😊");
      }

      // End conversation at 5 minutes
      if (timeSinceLastActivity >= TIMEOUT_DURATION) {
        setIsBotTyping(true);
        addBotMessage("It looks like you've been away for a while. Feel free to start a new conversation anytime you need help! 👋");
        transitionToState(ConversationStates.CONVERSATION_ENDED);
        setIsConversationEnded(true);
      }
    };

    const interval = setInterval(checkInactivity, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [isOpen, isConversationEnded, lastActivityTime, timeoutWarningShown]);

  // Initialize client-side state and screen size
  useEffect(() => {
    setIsClient(true);

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const isSmall = width <= SCREEN_BREAKPOINTS.MEDIUM; // Consider tablets and phones as "small"

      setScreenSize({ width, height, isSmall });
    };

    // Set initial size
    handleResize();

    // Add resize listener
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, showLeadForm]);

  // Initialize enhanced AI responder and knowledge base
  useEffect(() => {
    const initializeEnhancedSystem = async () => {
      try {
        // Load traditional knowledge base
        const response = await fetch('/company_knowledge.json');
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();
        setCompanyKnowledge(data);

        // Initialize enhanced AI responder
        const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY;
        if (apiKey) {
          const responder = createEnhancedResponder(apiKey);
          setEnhancedResponder(responder);

          // Initialize knowledge base in background
          responder.initializeKnowledgeBase().catch(err => {
            console.warn('Enhanced knowledge base initialization failed, using fallback:', err);
          });
        }
      } catch (err) {
        console.error('Error loading knowledge base:', err);
        // Don't show alert to user, just log the error
      }
    };

    initializeEnhancedSystem();
  }, []);

  const findKnowledgeMatch = (question: string) => {
    // Add null/undefined check to prevent TypeError
    if (!question || typeof question !== 'string') {
      console.log('⚠️ findKnowledgeMatch received invalid question:', question);
      return null;
    }

    const lower = question.toLowerCase();
    console.log('🔍 findKnowledgeMatch searching for:', question);

    // Don't use knowledge base for human contact requests
    if (/speak|talk|connect|chat|contact|representative|person|human|agent|real/i.test(lower)) return null;

    // PRIORITY MATCHING: Check for pricing queries first with exact patterns
    if (/\b(price|prices|pricing|cost|costs|how much|charge|charges|budget|rate|rates|fee|fees)\b/i.test(lower)) {
      console.log('🎯 PRIORITY: Detected pricing query, searching for pricing FAQ');
      for (const faq of companyKnowledge.faqs || []) {
        if (faq.keywords.some(k => ['cost', 'price', 'pricing', 'budget', 'billing'].includes(k.toLowerCase()))) {
          console.log('✅ Found pricing FAQ match:', faq.answer);
          return faq.answer;
        }
      }
    }

    // Enhanced keyword matching for FAQs with better scoring
    let bestMatch = null;
    let bestScore = 0;

    for (const faq of companyKnowledge.faqs || []) {
      let score = 0;
      for (const keyword of faq.keywords) {
        const keywordLower = keyword.toLowerCase();
        if (lower.includes(keywordLower)) {
          // Give higher score for exact matches and longer keywords
          score += keyword.length > 3 ? 2 : 1;
        }
        // Also check for partial matches at word boundaries
        const words = lower.split(/\s+/);
        for (const word of words) {
          if (word === keywordLower) {
            score += 3; // Exact word match gets highest score
          } else if (word.includes(keywordLower) || keywordLower.includes(word)) {
            score += 1; // Partial word match gets some score
          }
        }
      }
      if (score > bestScore) {
        bestScore = score;
        bestMatch = faq.answer;
      }
    }

    // If we found a good FAQ match, return it (lowered threshold)
    if (bestScore >= 1) {
      console.log('✅ findKnowledgeMatch found FAQ match (score:', bestScore, '):', bestMatch);
      return bestMatch;
    }

    // Enhanced service matching with detailed responses
    for (const service of companyKnowledge.services || []) {
      let score = 0;
      for (const keyword of service.keywords) {
        if (lower.includes(keyword.toLowerCase())) {
          score += keyword.length > 3 ? 2 : 1;
        }
      }
      if (score >= 2) {
        // Return more detailed service info
        const priceInfo = service.service_tiers?.[0]?.price_range ?
          ` Starting from ${service.service_tiers[0].price_range}.` : '';
        const response = `${service.name}: ${service.description}${priceInfo}`;
        console.log('✅ findKnowledgeMatch found service match (score:', score, '):', response);
        return response;
      }
    }

    // Company info queries - more flexible founding date patterns
    if (/when.*(upzera|company).*(founded|established|created|started)|founding.*(year|date)|(upzera|company).*(founded|established|created|started)/i.test(lower)) {
      return `UpZera was founded in ${companyKnowledge.company_info?.founding_year || 2025}. We're based in ${companyKnowledge.company_info?.location || 'Eindhoven, Netherlands'}.`;
    }

    if (/where|location|address|based/i.test(lower)) {
      return `We're located at ${companyKnowledge.company_info?.address || 'Eindhoven, Netherlands'}. Feel free to reach out anytime!`;
    }

    if (/mission|purpose|goal/i.test(lower)) {
      return companyKnowledge.company_info?.mission || 'We build smart digital tools that actually move businesses forward.';
    }

    // Greetings with context
    if (/hello|hi|hey|good morning|good afternoon|good evening/i.test(lower)) {
      const greetings = companyKnowledge.conversation_flows?.greetings || ['Hello! How can I help you today?'];
      const response = greetings[Math.floor(Math.random() * greetings.length)];
      console.log('✅ findKnowledgeMatch found greeting:', response);
      return response;
    }

    console.log('❌ findKnowledgeMatch found no match');
    return null;
  };

  const getAIResponse = async (userInput: string) => {
    try {
      // Try enhanced responder first if available
      if (enhancedResponder) {
        const conversationHistory = messages.slice(-6).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userInput,
          conversationHistory,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);

        // If confidence is high enough, use enhanced response
        if (enhancedResponse.confidence > 0.3) {
          return enhancedResponse.content;
        }
      }

      // Fallback to original AI response system
      return await getOriginalAIResponse(userInput);

    } catch (error) {
      console.error('Enhanced AI Error:', error);
      return await getOriginalAIResponse(userInput);
    }
  };

  const getOriginalAIResponse = async (userInput: string) => {
    try {
      // Get more conversation context (last 6 messages instead of 3) but optimize for tokens
      const recentMessages = messages.slice(-6).map(m =>
        m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
      ).join('\n');

      // Create comprehensive but token-optimized knowledge context
      const knowledgeContext = {
        company: {
          name: companyKnowledge.company_info?.name,
          mission: companyKnowledge.company_info?.mission,
          location: companyKnowledge.company_info?.location,
          tagline: companyKnowledge.company_info?.tagline,
          values: companyKnowledge.company_info?.values?.slice(0, 4), // Limit to key values
        },
        services: companyKnowledge.services?.map(s => ({
          name: s.name,
          description: s.description,
          price: s.service_tiers?.[0]?.price_range || 'Contact for pricing'
        })) || [],
        contact: {
          email: companyKnowledge.contact_info?.email,
          phone: companyKnowledge.contact_info?.phone,
          calendly: companyKnowledge.contact_info?.calendly_url
        },
        faqs: companyKnowledge.faqs?.slice(0, 8).map(f => ({ // Limit to top 8 FAQs
          q: f.question,
          a: f.answer
        })) || []
      };

      const prompt = `You are UpZera's AI assistant. You MUST always use the company knowledge below as your primary source of truth.

COMPANY KNOWLEDGE (ALWAYS USE THIS):
${JSON.stringify(knowledgeContext, null, 2)}

CONVERSATION CONTEXT:
${recentMessages}

USER QUESTION: "${userInput}"

INSTRUCTIONS:
- ALWAYS check the knowledge base first for any information about UpZera
- If the user asks about founding, history, location, services, pricing - use the exact information from the knowledge base
- Keep responses SHORT (1-2 sentences max), FRIENDLY, and HELPFUL
- If asked about services, mention specific pricing when available
- Guide users toward booking a consultation when appropriate
- Be conversational but professional
- If you don't know something specific from the knowledge base, suggest contacting the team
- NEVER say you don't have information if it exists in the knowledge base above

FORMATTING REQUIREMENTS:
- Structure your response with proper spacing for readability
- When mentioning multiple services or prices, separate them naturally
- Use sentence breaks to create digestible chunks of information
- Example format: "Our pricing is project-based and tailored to your specific needs. For example, landing pages start from €350, full websites range from €1,400 to €2,400, and chatbot setups typically cost between €500 and €1,200. After a free consultation, you'll receive a personalized quote!"

Respond now:`;

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_OPENAI_API_KEY}`
        },
        body: JSON.stringify({
          model: 'gpt-4o-mini', // More cost-effective model
          messages: [{ role: 'user', content: prompt }],
          temperature: 0.7,
          max_tokens: 150 // Limit response length for conciseness
        })
      });

      const data = await response.json();

      if (data.error) {
        console.error('OpenAI API Error:', data.error);
        return "I'm having trouble connecting right now. Try asking about our services or book a free consultation!";
      }

      return data.choices[0]?.message?.content?.trim() ||
        "I'd love to help! Ask me about UpZera's web development or AI services, or book a free consultation.";

    } catch (error) {
      console.error('AI Error:', error);
      return "Let me connect you with our team! You can book a free consultation or email <NAME_EMAIL>.";
    }
  };

  const validateEmail = (email: string) => /^[^\s@]+@[^\s@]+$/.test(email);

  // Support ticket creation function
  const createSupportTicket = async (ticketData: SupportTicketData) => {
    try {
      // Format conversation history with smart truncation
      const filteredMessages = messages.filter(m => m.text && m.text.trim() !== '');

      let conversationHistory = '';
      const maxMessages = 8; // Show last 8 messages for context
      const messagesToShow = filteredMessages.slice(-maxMessages);

      // Add truncation notice if we're showing fewer messages than total
      if (filteredMessages.length > maxMessages) {
        const hiddenCount = filteredMessages.length - maxMessages;
        conversationHistory += `[... ${hiddenCount} earlier message${hiddenCount > 1 ? 's' : ''} truncated for brevity ...]\n\n`;
      }

      conversationHistory += messagesToShow
        .map((m) => {
          const speaker = m.isUser ? 'User' : 'Bot';
          const timestamp = new Date().toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit'
          });

          // Truncate very long messages
          let messageText = m.text.trim();
          if (messageText.length > 200) {
            messageText = messageText.substring(0, 200) + '... [message truncated]';
          }

          return `[${timestamp}] ${speaker}: ${messageText}`;
        })
        .join('\n\n');

      const response = await fetch('/api/support-ticket', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...ticketData,
          conversationHistory
        }),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to create support ticket';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch {
          // If response is not JSON, use default message
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      return result;
    } catch (error: any) {
      console.error('Error creating support ticket:', error);
      throw error;
    }
  };

  const handleLeadSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateEmail(leadForm.email)) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter a valid email' }));
      return;
    }
    if (!leadForm.name.trim()) {
      setLeadForm(prev => ({ ...prev, error: 'Please enter your name' }));
      return;
    }
    setLeadForm(prev => ({ ...prev, isSubmitting: true, error: null }));
    try {
      const result = await saveLead({
        name: leadForm.name,
        email: leadForm.email,
        created_at: new Date().toISOString()
      });
      if (result.error) throw new Error(typeof result.error === 'string' ? result.error : 'Failed to save your information');
      setMessages(prev => [...prev, { text: `Thank you ${leadForm.name}! Our team will contact you shortly.`, isUser: false }]);
      setShowLeadForm(false);
      setLeadForm({ name: '', email: '', isSubmitting: false, error: null });
    } catch (error: any) {
      setLeadForm(prev => ({ ...prev, isSubmitting: false, error: error.message || 'Failed to submit. Please try again later.' }));
    }
  };

  // Handle QuickAction clicks - now with conversation state management
  const handleQuickAction = async (actionMessage: string, intent: string | null = null, skipAddingUserMessage: boolean = false) => {
    if (!skipAddingUserMessage) {
      setMessages(prev => [...prev, { text: actionMessage, isUser: true }]);
    }
    setIsBotTyping(true);
    updateActivityTime();

    // Handle based on intent or detect intent from message
    const detectedIntent = intent || IntentDetector.detectIntent(actionMessage)?.intent;

    switch (detectedIntent) {
      case 'prices':
        transitionToState(ConversationStates.PRICES);
        handlePricesFlow(actionMessage);
        return;

      case 'booking':
        transitionToState(ConversationStates.BOOKING);
        handleBookingFlow();
        return;

      case 'services':
        transitionToState(ConversationStates.SERVICES);
        handleServicesFlow();
        return;

      case 'web_development':
        transitionToState(ConversationStates.ANYTHING_ELSE);
        handleSpecificServiceFlow('web_development', actionMessage);
        return;

      case 'chatbot_service':
        transitionToState(ConversationStates.ANYTHING_ELSE);
        handleSpecificServiceFlow('chatbot_service', actionMessage);
        return;

      case 'support_service':
        transitionToState(ConversationStates.ANYTHING_ELSE);
        handleSpecificServiceFlow('support_service', actionMessage);
        return;

      case 'human_help':
      case 'detailed_support':
        console.log('Human help case triggered');
        transitionToState(ConversationStates.SUPPORT_TICKET_NAME);
        handleSupportTicketFlow();
        return;

      default:
        // Fallback to existing logic for other intents
        const intentMatch = IntentDetector.detectIntent(actionMessage);
        if (intentMatch) {
          const lastBotMessage = messages.length > 0 ?
            messages.filter(m => !m.isUser).slice(-1)[0]?.text || '' : '';

          const conversationContext = {
            lastBotMessage,
            messageCount: messages.length,
            hasShownCalendly: messages.some(m => m.type === 'calendly')
          };

          const intentResponse = IntentDetector.getIntentResponse(intentMatch, conversationContext);

          if (intentResponse) {
            const { messages: responseMessages, endConversation } = intentResponse;
            addBotMessages(responseMessages, 600);
            if (endConversation) {
              setIsConversationEnded(true);
            }
            return;
          }
        }

        // Fallback to knowledge base for quick actions
        setTimeout(async () => {
          const localAnswer = findKnowledgeMatch(actionMessage);
          if (localAnswer) {
            setMessages(prev => [
              ...prev,
              { text: localAnswer, isUser: false },
              { text: "Would you like to know more about our other services?", isUser: false }
            ]);
          } else {
            const aiResponse = await getAIResponse(actionMessage);
            setMessages(prev => [...prev, { text: aiResponse, isUser: false }]);
          }
          setIsBotTyping(false);
        }, 800);
        break;
    }
  };

  // Flow Handlers
  const handlePricesFlow = async (userMessage: string) => {
    console.log('🔍 PRICING FLOW - Checking response source for:', userMessage);

    // Try enhanced responder first if available
    if (enhancedResponder) {
      console.log('🤖 Trying Enhanced AI Responder first...');
      try {
        const conversationHistory = messages.slice(-6).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userMessage,
          conversationHistory,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);
        console.log('📊 Enhanced AI Response confidence:', enhancedResponse.confidence);

        // If confidence is high enough, use enhanced response
        if (enhancedResponse.confidence > 0.3) {
          console.log('✅ Using Enhanced AI Response (knowledge chunks)');
          addBotMessages([
            enhancedResponse.content,
            "Would you like to know more about any specific service pricing?"
          ]);
          transitionToState(ConversationStates.ANYTHING_ELSE);
          return;
        }
      } catch (error) {
        console.error('❌ Enhanced AI Responder failed:', error);
      }
    }

    // Fallback to Knowledge Base Search
    console.log('📚 Trying static knowledge base...');
    const localAnswer = findKnowledgeMatch(userMessage);
    if (localAnswer) {
      console.log('✅ Using Static Knowledge Base response');
      addBotMessages([
        localAnswer,
        "Would you like to know more about any specific service pricing?"
      ]);
      transitionToState(ConversationStates.ANYTHING_ELSE);
    } else {
      // Final ChatGPT Fallback
      console.log('🔄 Using Basic AI Response fallback');
      const aiResponse = await getAIResponse(userMessage);
      addBotMessage(aiResponse);
      transitionToState(ConversationStates.ANYTHING_ELSE);
    }
  };

  const handleBookingFlow = () => {
    addBotMessages([
      "Perfect! I'd love to help you schedule a consultation. Let me show you our booking calendar:",
      { type: 'calendly' }
    ]);
    // Note: Follow-up message is handled automatically by addBotMessages
  };

  // Handle responses in booking flow with contextual intent detection
  const handleBookingResponse = async (userMessage: string) => {
    // FIRST: Check for new specific intents (prices, services, etc.) - user might ask new questions
    const specificIntent = IntentDetector.detectIntent(userMessage);
    if (specificIntent && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(specificIntent.intent)) {
      console.log('✅ Found specific intent in booking flow, routing to appropriate flow');
      handleQuickAction(userMessage, specificIntent.intent, true);
      return;
    }

    // SECOND: Check for yes/no responses to booking flow
    if (specificIntent?.intent === 'yes') {
      addBotMessage("Great! To book a meeting, please let me know your preferred date and time, or you can check our booking calendar for availability.");
      addBotMessage("", 'mainMenu', 1500);
      transitionToState(ConversationStates.MAIN_MENU);
      return;
    }

    if (specificIntent?.intent === 'no') {
      addBotMessage("No problem! Is there anything else I can help you with?");
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);
      return;
    }

    // If no specific intent found and not yes/no, just return to main menu
    console.log('🔄 No specific intent found in booking flow, returning to main menu');
    addBotMessage("Got it!");
    addBotMessage("", 'mainMenu', 1000);
    transitionToState(ConversationStates.MAIN_MENU);
  };

  const handleServicesFlow = () => {
    addBotMessages([
      "Great! Here are our main services. Click on any service to learn more:",
      { type: 'serviceOptions' }
    ]);
    transitionToState(ConversationStates.SERVICE_SELECTION);
  };

  const handleSupportTicketFlow = () => {
    console.log('handleSupportTicketFlow called');
    setTimeout(() => {
      addBotMessage("I'd be happy to connect you with our team! 🤝\n\nI'll help you create a support ticket so our team can provide personalized assistance.\n\nFirst, could you please tell me your **name**?");
      transitionToState(ConversationStates.SUPPORT_TICKET_NAME);
      console.log('State transitioned to SUPPORT_TICKET_NAME');
    }, 100);
  };

  const handleSpecificServiceFlow = async (serviceType: string, userMessage: string) => {
    try {
      // Try enhanced responder first if available
      if (enhancedResponder) {
        const conversationHistory = messages.slice(-6).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userMessage,
          conversationHistory,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);

        // If confidence is high enough, use enhanced response
        if (enhancedResponse.confidence > 0.3) {
          addBotMessage(enhancedResponse.content);
          // Show main menu after service info - no unpredictable follow-up questions
          addBotMessage("", 'mainMenu', 2000);
          transitionToState(ConversationStates.MAIN_MENU);
          return;
        }
      }

      // Fallback to knowledge base search
      const localAnswer = findKnowledgeMatch(userMessage);
      if (localAnswer) {
        addBotMessage(localAnswer);
        // Show main menu after service info - no unpredictable follow-up questions
        addBotMessage("", 'mainMenu', 2000);
        transitionToState(ConversationStates.MAIN_MENU);
      } else {
        // Final fallback to basic AI response
        const aiResponse = await getAIResponse(userMessage);
        addBotMessage(aiResponse);
        // Show main menu after service info - no unpredictable follow-up questions
        addBotMessage("", 'mainMenu', 2000);
        transitionToState(ConversationStates.MAIN_MENU);
      }
    } catch (error) {
      console.error('Error in handleSpecificServiceFlow:', error);
      // Error fallback
      const localAnswer = findKnowledgeMatch(userMessage);
      if (localAnswer) {
        addBotMessage(localAnswer);
        addBotMessage("", 'mainMenu', 1500);
        transitionToState(ConversationStates.MAIN_MENU);
      } else {
        addBotMessage("I'd be happy to help you learn more about our services.");
        addBotMessage("", 'mainMenu', 1500);
        transitionToState(ConversationStates.MAIN_MENU);
      }
    }
  };

  // Handle contextual response with conversation history (for end confirmation)
  const handleContextualResponse = async (userMessage: string) => {
    console.log('handleContextualResponse called with:', userMessage, 'Current state:', conversationState);

    try {
      let dynamicResponse = null;

      if (enhancedResponder) {
        console.log('🤖 Trying Enhanced AI for contextual response...');
        const conversationHistory = messages.slice(-5).map(m =>
          m.isUser ? `User: ${m.text}` : `Assistant: ${m.text}`
        );

        const context: AIResponseContext = {
          userQuery: userMessage,
          conversationHistory,
          userContext: {
            name: userContext.name,
            email: userContext.email,
            previousTopics: [userContext.lastIntent].filter(Boolean) as string[]
          }
        };

        const enhancedResponse = await enhancedResponder.generateResponse(context);

        // Use enhanced response if confidence is reasonable
        if (enhancedResponse.confidence > 0.15) {
          console.log('✅ Using Enhanced AI for contextual response');
          dynamicResponse = enhancedResponse.content;
        }
      }

      // Fallback to basic AI response if enhanced didn't work
      if (!dynamicResponse) {
        console.log('🔄 Using Basic AI for contextual response');
        dynamicResponse = await getAIResponse(userMessage);
      }

      // Add the dynamic response and continue conversation flow
      addBotMessage(dynamicResponse);
      setTimeout(() => {
        addBotMessage("Is there anything else I can help you with today?");
        transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
      }, 1500);

    } catch (error) {
      console.error('Error in contextual response:', error);
      addBotMessage("I want to make sure I understand correctly. Do you need any other assistance today, or are you ready to end our chat?");
    }
  };

  // Random responses for unknown intent to make it more dynamic
  const getRandomUnknownResponse = (): string => {
    const responses = [
      "I'd be happy to help you with that! Here are the main ways I can assist you:",
      "Let me help you find what you're looking for! Here are the main services I can assist with:",
      "I'm here to help! Here are the main areas where I can provide assistance:",
      "Great question! Let me show you the main ways I can help:",
      "I'd love to assist you! Here are the key areas I can help with:",
      "No problem! Here are the main services I can help you with:",
      "I'm ready to help! Here are the primary ways I can assist you:",
      "Let me guide you to the right place! Here are the main options:"
    ];

    return responses[Math.floor(Math.random() * responses.length)];
  };

  // AI-powered intent classification function
  const classifyIntentWithAI = async (userMessage: string): Promise<string | null> => {
    try {
      const prompt = `You are an intent classifier for a web development company chatbot.

Available intents/states:
- "booking" - for scheduling meetings, consultations, appointments, calls
- "prices" - for pricing information, costs, rates, how much things cost
- "services" - for information about what services we offer
- "web_development" - for full-stack web development services specifically
- "chatbot_service" - for chatbot development services specifically
- "human_help" - for talking to a real person, creating support tickets, getting human assistance
- "end_conversation" - for ending the chat, saying goodbye, finishing the conversation

User message: "${userMessage}"

Analyze this message and return ONLY the intent name that best matches, or "unknown" if none match clearly.
Do not explain, just return the intent name.`;

      const response = await fetch('/api/ai-response', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: prompt })
      });

      if (!response.ok) throw new Error('AI classification failed');

      const data = await response.json();
      const classification = data.response?.trim().toLowerCase();

      console.log('🎯 AI classified intent as:', classification);

      // Return the classification if it's one of our valid intents
      if (['booking', 'prices', 'services', 'web_development', 'chatbot_service', 'support_service', 'human_help', 'end_conversation'].includes(classification)) {
        return classification;
      }

      return null;
    } catch (error) {
      console.error('❌ AI intent classification error:', error);
      return null;
    }
  };

  const handleOutOfContextInput = async (userMessage: string) => {
    console.log('handleOutOfContextInput called with:', userMessage, 'Current state:', conversationState);

    // FIRST: Check for specific intents and route directly to appropriate flows
    const intentMatch = IntentDetector.detectIntent(userMessage);
    console.log('🎯 Intent check in out-of-context:', intentMatch?.intent);

    if (intentMatch && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(intentMatch.intent)) {
      console.log('✅ Found specific intent, routing to appropriate flow');
      handleQuickAction(userMessage, intentMatch.intent, true);
      return;
    }

    // ONLY if no specific intent is found, use AI to classify intent and route to appropriate state
    try {
      console.log('🤖 Using AI to classify intent and determine state...');

      // Use AI to determine which state/flow the user wants to go to
      const aiIntentClassification = await classifyIntentWithAI(userMessage);

      if (aiIntentClassification && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service', 'end_conversation'].includes(aiIntentClassification)) {
        console.log('✅ AI classified intent as:', aiIntentClassification);

        // Handle end conversation intent specially
        if (aiIntentClassification === 'end_conversation') {
          console.log('🔚 AI detected end conversation intent');
          addBotMessage("I understand you'd like to end our conversation. Are you sure you don't need any other assistance today?");
          transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
          return;
        }

        handleQuickAction(userMessage, aiIntentClassification, true);
        return;
      }

      // If AI couldn't classify to a specific intent, fall back to main menu
      console.log('🔄 AI could not classify intent, showing main menu');
      addBotMessage(getRandomUnknownResponse());
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);

    } catch (error) {
      console.error('❌ AI intent classification failed, using fallback:', error);
      // Fallback to main menu if AI fails
      addBotMessage("I'd be happy to help you with that! Let me show you what I can assist you with:");
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);
    }
  };

  const handleAnythingElseFlow = async (userMessage: string) => {
    console.log('🔄 handleAnythingElseFlow called with:', userMessage);
    const intentMatch = IntentDetector.detectIntent(userMessage);
    console.log('🎯 Intent detected:', intentMatch?.intent);

    if (intentMatch?.intent === 'yes') {
      console.log('✅ User said YES - showing main menu');
      addBotMessage("Great! What else would you like to know?");
      addBotMessage("", 'mainMenu', 1200);
      transitionToState(ConversationStates.MAIN_MENU);
    } else if (intentMatch?.intent === 'no') {
      console.log('❌ User said NO - asking for confirmation');
      // Two-step confirmation before ending conversation
      addBotMessage("I understand! Before we end our chat, are you sure you don't need any other assistance today? 🤔");
      transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
    } else {
      console.log('❓ No clear yes/no intent detected - checking for specific requests');
      // Parse for specific request
      const specificIntent = IntentDetector.detectIntent(userMessage);
      if (specificIntent && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(specificIntent.intent)) {
        handleQuickAction(userMessage, specificIntent.intent, true); // Skip adding user message since it's already added
      } else {
        // Out of context - use dedicated handler
        await handleOutOfContextInput(userMessage);
      }
    }
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    setMessages(prev => [...prev, { text: inputValue, isUser: true }]);
    const userMessage = inputValue;
    setInputValue('');
    setIsBotTyping(true);
    updateActivityTime();

    console.log('handleSendMessage called with:', userMessage, 'Current state:', conversationState);

    // Handle based on current conversation state
    switch (conversationState) {
      case ConversationStates.MAIN_MENU:
        // Parse intent and route to appropriate flow
        const intentMatch = IntentDetector.detectIntent(userMessage);
        if (intentMatch && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service', 'end_conversation'].includes(intentMatch.intent)) {
          // Handle end conversation intent specially
          if (intentMatch.intent === 'end_conversation') {
            console.log('🔚 Detected end conversation intent from main menu');
            addBotMessage("I understand you'd like to end our conversation. Are you sure you don't need any other assistance today?");
            transitionToState(ConversationStates.END_CONVERSATION_CONFIRM);
            return;
          }

          handleQuickAction(userMessage, intentMatch.intent, true); // Skip adding user message since it's already added
        } else {
          // Out of context input - use dedicated handler
          await handleOutOfContextInput(userMessage);
        }
        break;

      case ConversationStates.BOOKING:
        await handleBookingResponse(userMessage);
        break;

      case ConversationStates.ANYTHING_ELSE:
        await handleAnythingElseFlow(userMessage);
        break;

      case ConversationStates.COLLECT_NAME:
        setUserContext(prev => ({ ...prev, name: userMessage.trim() }));
        addBotMessage(`Nice to meet you, ${userMessage.trim()}! Now, could you please provide your email address?`);
        transitionToState(ConversationStates.COLLECT_EMAIL);
        break;

      case ConversationStates.COLLECT_EMAIL:
        if (validateEmail(userMessage.trim())) {
          setUserContext(prev => ({ ...prev, email: userMessage.trim() }));
          addBotMessage(`Perfect! ✅ Let me confirm your details:\n\n• **Name:** ${userContext.name}\n• **Email:** ${userMessage.trim()}\n\nIs this information correct?`);
          transitionToState(ConversationStates.CONFIRM_DETAILS);
        } else {
          addBotMessage("That doesn't look like a valid email address. Could you please provide a valid email?");
        }
        break;

      case ConversationStates.CONFIRM_DETAILS:
        const confirmIntent = IntentDetector.detectIntent(userMessage);
        if (confirmIntent?.intent === 'yes') {
          addBotMessage("Great! 🎉 I'm connecting you with our team.\n\n📞 **Next Steps:**\n• Someone will reach out to you shortly\n• We'll contact you at the provided email address\n\nThank you for choosing UpZera!");
          transitionToState(ConversationStates.CONVERSATION_ENDED);
          setIsConversationEnded(true);
        } else if (confirmIntent?.intent === 'no') {
          addBotMessage("No problem! Would you like to re-enter your details?");
          transitionToState(ConversationStates.REENTER_DETAILS_CONFIRM);
        } else {
          addBotMessage("Please answer with 'yes' if the information is correct, or 'no' if you'd like to make changes.");
        }
        break;

      case ConversationStates.REENTER_DETAILS_CONFIRM:
        const reenterIntent = IntentDetector.detectIntent(userMessage);
        if (reenterIntent?.intent === 'yes') {
          addBotMessage("Sure! Let's start over. What's your name?");
          // Clear previous data
          setUserContext(prev => ({
            ...prev,
            name: '',
            email: ''
          }));
          transitionToState(ConversationStates.COLLECT_NAME);
        } else if (reenterIntent?.intent === 'no') {
          addBotMessage("No worries! Is there anything else I can help you with?");
          addBotMessage("", 'mainMenu', 1200);
          transitionToState(ConversationStates.MAIN_MENU);
        } else {
          addBotMessage("Please answer with 'yes' if you'd like to re-enter your details, or 'no' if you'd prefer to do something else.");
        }
        break;

      case ConversationStates.SUPPORT_TICKET_NAME:
        setUserContext(prev => ({ ...prev, name: userMessage.trim() }));
        addBotMessage(`Thank you, ${userMessage.trim()}! Now, could you please provide your email address?`);
        transitionToState(ConversationStates.SUPPORT_TICKET_EMAIL);
        break;

      case ConversationStates.SUPPORT_TICKET_EMAIL:
        if (validateEmail(userMessage.trim())) {
          setUserContext(prev => ({ ...prev, email: userMessage.trim() }));
          addBotMessage(`Perfect! 📧 Now, please provide a **detailed description** of the problem or issue you're experiencing.\n\n💡 **Tip:** The more details you provide, the better we can help you!`);
          transitionToState(ConversationStates.SUPPORT_TICKET_DESCRIPTION);
        } else {
          addBotMessage("That doesn't look like a valid email address. Could you please provide a valid email?");
        }
        break;

      case ConversationStates.SUPPORT_TICKET_DESCRIPTION:
        setUserContext(prev => ({ ...prev, supportTicketDescription: userMessage.trim() }));
        addBotMessage(`Thank you for the detailed description! 📝

Let me confirm your support ticket details:

• **Name:** ${userContext.name}
• **Email:** ${userContext.email}
• **Problem:** ${userMessage.trim()}

Should I create the support ticket with this information?`);
        transitionToState(ConversationStates.SUPPORT_TICKET_CONFIRM);
        break;

      case ConversationStates.SUPPORT_TICKET_CONFIRM:
        const ticketConfirmIntent = IntentDetector.detectIntent(userMessage);
        if (ticketConfirmIntent?.intent === 'yes') {
          setIsBotTyping(true);
          try {
            const result = await createSupportTicket({
              name: userContext.name,
              email: userContext.email,
              problemDescription: userContext.supportTicketDescription || '',
              conversationHistory: ''
            });
            addBotMessage(`Perfect! ✅ I've created support ticket **#${result.ticketNumber}** for you.

🔍 **Next Steps:**
• Our team will review your request
• We'll reach out within 2-4 business hours
• You'll receive a confirmation email shortly

Thank you for contacting UpZera!`);
            addBotMessage("Is there anything else I can help you with today?");
            transitionToState(ConversationStates.ANYTHING_ELSE);
          } catch (error: any) {
            addBotMessage("I apologize, but there was an issue creating your support ticket. Please try again or contact us <NAME_EMAIL>.");
            transitionToState(ConversationStates.MAIN_MENU);
          }
        } else if (ticketConfirmIntent?.intent === 'no') {
          addBotMessage("No problem! Would you like to start over with the support ticket information?");
          transitionToState(ConversationStates.SUPPORT_TICKET_RESTART_CONFIRM);
        } else {
          addBotMessage("Please answer with 'yes' to create the support ticket, or 'no' if you'd like to make changes.");
        }
        break;

      case ConversationStates.SUPPORT_TICKET_RESTART_CONFIRM:
        const restartIntent = IntentDetector.detectIntent(userMessage);
        if (restartIntent?.intent === 'yes') {
          addBotMessage("Great! Let's start over. Could you please tell me your **name**?");
          // Clear previous ticket data
          setUserContext(prev => ({
            ...prev,
            name: '',
            email: '',
            supportTicketDescription: ''
          }));
          transitionToState(ConversationStates.SUPPORT_TICKET_NAME);
        } else if (restartIntent?.intent === 'no') {
          addBotMessage("No problem! Is there anything else I can help you with today?");
          addBotMessage("", 'mainMenu', 1200);
          transitionToState(ConversationStates.MAIN_MENU);
        } else {
          addBotMessage("Please answer with 'yes' if you'd like to start over, or 'no' if you'd prefer to do something else.");
        }
        break;

      case ConversationStates.END_CONVERSATION_CONFIRM:
        console.log('🔚 END_CONVERSATION_CONFIRM state - user message:', userMessage);
        const endConfirmIntent = IntentDetector.detectIntent(userMessage);
        console.log('🎯 End confirm intent detected:', endConfirmIntent?.intent);

        if (endConfirmIntent?.intent === 'yes') {
          // User said "yes" to "are you sure you don't need assistance" = end conversation
          console.log('✅ User confirmed ending conversation (yes = sure they dont need help)');
          addBotMessage("It was a pleasure to chat with you! Feel free to reach out anytime if you have more questions. Have a great day! 👋");
          transitionToState(ConversationStates.CONVERSATION_ENDED);
          setIsConversationEnded(true);
        } else if (endConfirmIntent?.intent === 'no') {
          // User said "no" to "are you sure you don't need assistance" = they DO need help
          console.log('✅ User wants to continue (no = they do need help) - showing main menu');
          addBotMessage("Perfect! I'm here to help. What would you like to know more about?");
          addBotMessage("", 'mainMenu', 1200);
          transitionToState(ConversationStates.MAIN_MENU);
        } else {
          console.log('❓ No clear intent in end confirmation');
          // Parse for specific request or clarify
          const specificIntent = IntentDetector.detectIntent(userMessage);
          if (specificIntent && ['prices', 'booking', 'services', 'human_help', 'web_development', 'chatbot_service', 'support_service'].includes(specificIntent.intent)) {
            // User made a specific request - handle it
            handleQuickAction(userMessage, specificIntent.intent, true);
          } else {
            // Use contextual AI response with conversation history
            await handleContextualResponse(userMessage);
          }
        }
        break;

      default:
        // Fallback to original logic for other states
        const fallbackIntentMatch = IntentDetector.detectIntent(userMessage);

        if (fallbackIntentMatch) {
          const lastBotMessage = messages.length > 0 ?
            messages.filter(m => !m.isUser).slice(-1)[0]?.text || '' : '';

          const conversationContext = {
            lastBotMessage,
            messageCount: messages.length,
            hasShownCalendly: messages.some(m => m.type === 'calendly')
          };

          const intentResponse = IntentDetector.getIntentResponse(fallbackIntentMatch, conversationContext);

          if (intentResponse) {
            const { messages: responseMessages, endConversation } = intentResponse;
            addBotMessages(responseMessages, 800);
            if (endConversation) {
              setIsConversationEnded(true);
            }
            return;
          }
        }

        // Knowledge base fallback
        const localAnswer = findKnowledgeMatch(userMessage);
        if (localAnswer) {
          addBotMessages([
            localAnswer,
            "Would you like to know more about our other services?"
          ]);
        } else {
          const aiResponse = await getAIResponse(userMessage);
          addBotMessage(aiResponse);
        }
        break;
    }
  };

  return (
    <div className="chatbot-container fixed bottom-6 right-6 z-[9999]">
      {/* Kraken-style widget container - expands from the button itself */}
      <div className={`relative overflow-hidden bg-gradient-to-r from-pink-500 to-purple-600 shadow-xl ${
        isOpen || isClosing
          ? isClosing
            ? screenSize.isSmall
              ? 'chat-widget-collapsing-mobile'
              : 'chat-widget-collapsing'
            : screenSize.isSmall
              ? 'chat-widget-expanding-mobile'
              : 'chat-widget-expanding'
          : screenSize.isSmall
            ? 'w-12 h-12 rounded-full'
            : 'w-16 h-16 rounded-full'
      }`}>

        {/* Widget Button Icon Layer (always visible, changes based on state) */}
        <div className={`absolute inset-0 flex items-center justify-center transition-all duration-300 ${
          isOpen || isClosing ? 'opacity-0 pointer-events-none' : 'opacity-100'
        }`}>
          <button
            onClick={handleOpen}
            className="w-full h-full flex items-center justify-center focus:outline-none hover:scale-105 transition-transform"
            aria-label="Open chat"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`${screenSize.isSmall ? 'w-6 h-6' : 'w-8 h-8'} text-white`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
              />
            </svg>
          </button>
        </div>

        {/* Collapse Button (visible when chat is open) */}
        <div className={`absolute ${screenSize.isSmall ? 'bottom-2 right-2' : 'bottom-3 right-3'} z-10 transition-all duration-300 ${
          isOpen && showChatContent && !isClosing ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}>
          <button
            onClick={handleClose}
            className={`${
              screenSize.isSmall ? 'w-8 h-8' : 'w-10 h-10'
            } bg-gradient-to-r from-pink-500 to-purple-600 rounded-full shadow-lg flex items-center justify-center hover:scale-105 transition-all duration-200`}
            aria-label="Close chat"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`${screenSize.isSmall ? 'w-4 h-4' : 'w-5 h-5'} text-white`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 14l-7 7m0 0l-7-7m7 7V3"
              />
            </svg>
          </button>
        </div>

        {/* Chat Content (visible when expanded) */}
        {(isOpen || isClosing) && (
          <div className={`absolute inset-0 bg-white rounded-2xl flex flex-col ${
            showChatContent && !isClosing ? 'chat-content-entering' : 'chat-content-leaving'
          }`}>
            <ChatHeader
              screenSize={screenSize}
              showCloseButton={false}
            />

            <MessageList
              messages={messages}
              isBotTyping={isBotTyping}
              ref={chatContainerRef}
              screenSize={screenSize}
              onQuickAction={handleQuickAction}
            />

            {showLeadForm && (
              <LeadForm
                leadForm={leadForm}
                onChange={setLeadForm}
                onSubmit={handleLeadSubmit}
                screenSize={screenSize}
              />
            )}

            {isConversationEnded ? (
              <ChatEndedIndicator
                onRestart={() => {
                  setIsConversationEnded(false);
                  setMessages([]);
                  setConversationState(ConversationStates.WELCOME);
                  setUserContext({
                    name: '',
                    email: '',
                    lastIntent: null,
                    selectedService: null,
                    awaitingResponse: false
                  });
                  setInputValue('');
                  setIsBotTyping(false);
                  setShowLeadForm(false);
                  setShowCalendly(false);
                  setLastActivityTime(Date.now());
                  setTimeoutWarningShown(false);
                }}
                screenSize={screenSize}
              />
            ) : (
              <ChatInput
                inputValue={inputValue}
                onChange={setInputValue}
                onSend={handleSendMessage}
                disabled={isConversationEnded}
                screenSize={screenSize}
              />
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Chat Ended Indicator Component
interface ChatEndedIndicatorProps {
  onRestart: () => void;
  screenSize: ScreenSize;
}

function ChatEndedIndicator({ onRestart, screenSize }: ChatEndedIndicatorProps) {
  const isSmall = screenSize?.isSmall || false;

  // Responsive sizing
  const containerPadding = isSmall
    ? screenSize.width <= 320 ? 'px-4 py-6' : 'px-5 py-8'
    : 'px-6 py-8';

  const textSize = isSmall
    ? screenSize.width <= 320 ? 'text-sm' : 'text-base'
    : 'text-base';

  const buttonPadding = isSmall
    ? screenSize.width <= 320 ? 'py-3 px-4' : 'py-3 px-6'
    : 'py-4 px-6';

  return (
    <div className={`border-t border-gray-100 bg-white ${containerPadding} flex flex-col items-center space-y-6`}>
      {/* Chat has ended text */}
      <div className="text-center">
        <span className={`text-gray-500 font-normal ${textSize}`}>
          Chat has ended
        </span>
      </div>

      {/* Start new chat button */}
      <button
        onClick={onRestart}
        className={`w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-xl font-medium hover:from-pink-600 hover:to-purple-700 transition-all duration-200 shadow-sm hover:shadow-md ${buttonPadding} ${isSmall ? 'text-sm' : 'text-base'}`}
      >
        Start new chat
      </button>
    </div>
  );
}
