import React from 'react';
import { ScreenSize } from './types';

interface Action {
  id: string;
  text: string;
  message: string;
}

interface QuickActionsProps {
  onActionClick: (message: string) => void;
  screenSize: ScreenSize;
}

export default function QuickActions({ onActionClick, screenSize }: QuickActionsProps) {
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust sizing based on screen size
  let buttonPadding = 'px-4 py-2';
  let buttonText = 'text-sm';
  let gridCols = 'grid-cols-2';
  let spacing = 'gap-2';
  
  if (isSmall) {
    if (width <= 320) {
      buttonPadding = 'px-2 py-1.5';
      buttonText = 'text-xs';
      gridCols = 'grid-cols-1';
      spacing = 'gap-1.5';
    } else if (width <= 375) {
      buttonPadding = 'px-3 py-1.5';
      buttonText = 'text-xs';
      gridCols = 'grid-cols-2';
      spacing = 'gap-1.5';
    } else {
      buttonPadding = 'px-3 py-2';
      buttonText = 'text-sm';
      gridCols = 'grid-cols-2';
      spacing = 'gap-2';
    }
  }

  const actions: Action[] = [
    {
      id: 'services',
      text: '🚀 Our Services',
      message: 'Tell me about your services'
    },
    {
      id: 'pricing',
      text: '💰 Pricing',
      message: 'What are your prices?'
    },
    {
      id: 'consultation',
      text: '📅 Book Meeting',
      message: 'I want to book a consultation'
    },
    {
      id: 'portfolio',
      text: '💼 Portfolio',
      message: 'Show me your previous work'
    }
  ];

  const handleActionClick = (action: Action) => {
    onActionClick(action.message);
  };

  return (
    <div className="w-full animate-fade-in mb-4">
      <div className="bg-gray-50 rounded-lg p-3">
        <p className={`text-gray-600 mb-3 ${buttonText} text-center`}>
          What would you like to know about?
        </p>
        <div className={`grid ${gridCols} ${spacing}`}>
          {actions.map((action) => (
            <button
              key={action.id}
              onClick={() => handleActionClick(action)}
              className={`
                ${buttonPadding} ${buttonText}
                bg-white border border-gray-200 rounded-lg
                hover:border-pink-300 hover:bg-pink-50
                transition-all duration-200
                text-gray-700 hover:text-pink-600
                shadow-sm hover:shadow-md
                font-medium
                flex items-center justify-center
                min-h-[40px]
              `}
            >
              {action.text}
            </button>
          ))}
        </div>
        <div className="mt-3 text-center">
          <button
            onClick={() => onActionClick('I want to schedule a consultation')}
            className={`
              ${buttonPadding} ${buttonText}
              bg-gradient-to-r from-pink-500 to-purple-600
              text-white rounded-lg font-medium
              hover:from-pink-600 hover:to-purple-700
              transition-all duration-200
              shadow-md hover:shadow-lg
              transform hover:scale-105
            `}
          >
            🎯 Get Started - Free Consultation
          </button>
        </div>
      </div>
    </div>
  );
}
