import React from 'react';
import { ScreenSize } from './types';

interface ChatHeaderProps {
  onClose?: () => void;
  screenSize: ScreenSize;
  showCloseButton?: boolean;
}

export default function ChatHeader({ onClose, screenSize, showCloseButton = true }: ChatHeaderProps) {
  // Get screen size properties
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;
  
  // Adjust sizes based on screen size
  let logoSize = 'w-12 h-12';
  let headerPadding = 'p-3';
  let titleFontSize = 'text-base';
  let statusSize = 'text-xs';
  let closeIconSize = 'h-5 w-5';
  
  if (isSmall) {
    if (width <= 320) { // Extra small screens
      logoSize = 'w-8 h-8';
      headerPadding = 'p-1.5';
      titleFontSize = 'text-sm';
      statusSize = 'text-[9px]';
      closeIconSize = 'h-4 w-4';
    } else if (width <= 375) { // Small screens
      logoSize = 'w-10 h-10';
      headerPadding = 'p-2';
      titleFontSize = 'text-sm';
      statusSize = 'text-[10px]';
      closeIconSize = 'h-4 w-4';
    } else { // Medium screens
      logoSize = 'w-11 h-11';
      headerPadding = 'p-2.5';
      titleFontSize = 'text-base';
      statusSize = 'text-xs';
      closeIconSize = 'h-5 w-5';
    }
  }

  return (
    <div className={`bg-gradient-to-r from-pink-500 to-purple-600 text-white rounded-t-2xl ${headerPadding}`}>
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <img 
            src="/logo/UpZera_chatbot_logo.svg" 
            alt="UpZera Logo" 
            className={`${logoSize} rounded-full bg-purple-600 p-1 border-2 border-purple-600 shadow-lg`} 
          />
          <div>
            <div className={`font-bold ${titleFontSize}`}>UpBot</div>
            <div className={`flex items-center space-x-1 text-green-400 ${statusSize}`}>
              <span className="w-1.5 h-1.5 bg-green-400 rounded-full"></span>
              <span>Online Now</span>
            </div>
          </div>
        </div>
        {showCloseButton && onClose && (
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className={closeIconSize}
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
}
