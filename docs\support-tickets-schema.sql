-- Support Tickets Table Schema for Supabase
-- Run this SQL in your Supabase SQL Editor to create the support_tickets table

CREATE TABLE IF NOT EXISTS support_tickets (
    id BIGSERIAL PRIMARY KEY,
    ticket_number VARCHAR(50) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    problem_description TEXT NOT NULL,
    conversation_history TEXT,
    status VARCHAR(50) DEFAULT 'open',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on ticket_number for faster lookups
CREATE INDEX IF NOT EXISTS idx_support_tickets_ticket_number ON support_tickets(ticket_number);

-- Create an index on email for faster customer lookups
CREATE INDEX IF NOT EXISTS idx_support_tickets_email ON support_tickets(email);

-- Create an index on status for filtering
CREATE INDEX IF NOT EXISTS idx_support_tickets_status ON support_tickets(status);

-- Create an index on created_at for sorting
CREATE INDEX IF NOT EXISTS idx_support_tickets_created_at ON support_tickets(created_at);

-- Add a trigger to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_support_tickets_updated_at 
    BEFORE UPDATE ON support_tickets 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add Row Level Security (RLS) policies if needed
-- Note: Adjust these policies based on your security requirements

-- Enable RLS
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

-- Policy to allow service role to do everything (for API operations)
CREATE POLICY "Service role can manage support tickets" ON support_tickets
    FOR ALL USING (auth.role() = 'service_role');

-- Policy to allow authenticated users to view their own tickets (optional)
-- Uncomment if you want users to be able to view their tickets through a dashboard
-- CREATE POLICY "Users can view their own tickets" ON support_tickets
--     FOR SELECT USING (auth.email() = email);

-- Add comments for documentation
COMMENT ON TABLE support_tickets IS 'Stores support tickets created through the chatbot system';
COMMENT ON COLUMN support_tickets.ticket_number IS 'Unique ticket identifier shown to customers';
COMMENT ON COLUMN support_tickets.name IS 'Customer name';
COMMENT ON COLUMN support_tickets.email IS 'Customer email address';
COMMENT ON COLUMN support_tickets.problem_description IS 'Detailed description of the customer problem';
COMMENT ON COLUMN support_tickets.conversation_history IS 'Chat conversation history leading to ticket creation';
COMMENT ON COLUMN support_tickets.status IS 'Ticket status: open, in_progress, resolved, closed';
COMMENT ON COLUMN support_tickets.created_at IS 'When the ticket was created';
COMMENT ON COLUMN support_tickets.updated_at IS 'When the ticket was last updated';
