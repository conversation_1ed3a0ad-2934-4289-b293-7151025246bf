import React from 'react';
import { ScreenSize } from './types';

interface Service {
  id: string;
  icon: string;
  title: string;
  message: string;
}

interface ServiceOptionsProps {
  onServiceClick: (message: string) => void;
  screenSize: ScreenSize;
}

export default function ServiceOptions({ onServiceClick, screenSize }: ServiceOptionsProps) {
  const isSmall = screenSize?.isSmall || false;
  const width = screenSize?.width || 1200;

  // Adjust sizing based on screen size
  let buttonText = 'text-sm';
  let spacing = 'gap-3';

  if (isSmall) {
    if (width <= 320) {
      buttonText = 'text-xs';
      spacing = 'gap-2';
    } else if (width <= 375) {
      buttonText = 'text-xs';
      spacing = 'gap-2';
    } else {
      buttonText = 'text-sm';
      spacing = 'gap-2.5';
    }
  }

  const services: Service[] = [
    {
      id: 'web-development',
      icon: '🚀',
      title: 'Full-Stack Web Development',
      message: 'Tell me about your web development services'
    },
    {
      id: 'chatbots',
      icon: '🤖',
      title: 'Chatbot Lead Generation',
      message: 'I want to know about your chatbot services'
    }
  ];

  const handleServiceClick = (service: Service) => {
    onServiceClick(service.message);
  };

  return (
    <div className="w-full animate-fade-in mb-4">
      <div className="bg-gray-50 rounded-lg p-3">
        <p className={`text-gray-600 mb-3 ${buttonText} text-center font-medium`}>
          Choose a service to learn more:
        </p>
        <div className={`space-y-2 ${spacing}`}>
          {services.map((service) => (
            <button
              key={service.id}
              onClick={() => handleServiceClick(service)}
              className={`
                w-full px-4 py-3
                bg-gradient-to-r from-pink-100 to-purple-100
                border border-pink-200
                rounded-full
                hover:from-pink-200 hover:to-purple-200
                hover:border-pink-300
                transition-all duration-200
                text-center
                shadow-sm hover:shadow-md
                group
              `}
            >
              <div className="flex items-center justify-center space-x-2">
                <span className="text-lg group-hover:scale-110 transition-transform duration-200">
                  {service.icon}
                </span>
                <span className="text-sm font-medium text-gray-800 group-hover:text-pink-600 transition-colors duration-200">
                  {service.title}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
