import React from 'react';
import { LeadFormData, ScreenSize } from './types';

interface LeadFormProps {
  leadForm: LeadFormData;
  onChange: (data: LeadFormData) => void;
  onSubmit: (e: React.FormEvent) => void;
  screenSize: ScreenSize;
}

export default function LeadForm({ leadForm, onChange, onSubmit, screenSize }: LeadFormProps) {
  return (
    <div className="mr-auto w-[90%] animate-fade-in">
      <form onSubmit={onSubmit} className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
        <h3 className="text-lg font-medium mb-3 text-gray-800">Contact Information</h3>
        <div className="space-y-3">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
            <input
              type="text"
              value={leadForm.name}
              onChange={(e) => onChange({ ...leadForm, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
            <input
              type="email"
              value={leadForm.email}
              onChange={(e) => onChange({ ...leadForm, email: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              required
            />
          </div>
          {leadForm.error && (
            <div className="text-red-500 text-sm animate-fade-in space-y-1">
              <p>{leadForm.error}</p>
            </div>
          )}
          <button
            type="submit"
            disabled={leadForm.isSubmitting}
            className="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-2 px-4 rounded-md hover:opacity-90 disabled:opacity-70 transition-opacity"
          >
            {leadForm.isSubmitting ? 'Submitting...' : 'Submit'}
          </button>
        </div>
      </form>
    </div>
  );
}
