/* ...existing code... */

/* Responsive mobile view for process flow */
@media (max-width: 767px) {
  .process-block {
    width: 100%;
    margin-bottom: 10px;
  }

  .pulsing-arrow {
    margin: 5px 0 15px;
  }

  /* No mobile connections */
}

/* Ensure smooth transitions */
.process-flow * {
  transition: all 0.3s ease-in-out;
}

/* No mobile connections for approach page */

/* Animation for wiggly line */
@keyframes dash {
  to {
    stroke-dashoffset: 100;
  }
}

/* Three-Step Chatbot Widget Animations */

/* Step 1 -> Step 2: Widget splits into intermediate blob */
@keyframes chat-widget-split {
  from {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    transform: scale(1) translateY(0);
  }
  to {
    width: 8rem;
    height: 6rem;
    border-radius: 2.5rem 2.5rem 2rem 2rem;
    transform: scale(1) translateY(-1rem);
  }
}

@keyframes chat-widget-split-mobile {
  from {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    transform: scale(1) translateY(0);
  }
  to {
    width: 6rem;
    height: 4.5rem;
    border-radius: 2rem 2rem 1.5rem 1.5rem;
    transform: scale(1) translateY(-0.75rem);
  }
}

/* Step 2 -> Step 3: Blob expands upward into full chat window */
@keyframes chat-widget-expand-upward {
  from {
    width: 8rem;
    height: 6rem;
    border-radius: 2.5rem 2.5rem 2rem 2rem;
    transform: scale(1) translateY(-1rem);
  }
  to {
    width: 384px;
    height: 600px;
    border-radius: 1rem;
    transform: scale(1) translateY(-320px);
  }
}

@keyframes chat-widget-expand-upward-mobile {
  from {
    width: 6rem;
    height: 4.5rem;
    border-radius: 2rem 2rem 1.5rem 1.5rem;
    transform: scale(1) translateY(-0.75rem);
  }
  to {
    width: calc(100vw - 2rem);
    height: calc(100vh - 8rem);
    border-radius: 1rem;
    transform: scale(1) translateY(calc(-50vh + 2rem));
  }
}

/* Reverse animations for closing */
@keyframes chat-widget-collapse-downward {
  from {
    width: 384px;
    height: 600px;
    border-radius: 1rem;
    transform: scale(1) translateY(-320px);
  }
  to {
    width: 8rem;
    height: 6rem;
    border-radius: 2.5rem 2.5rem 2rem 2rem;
    transform: scale(1) translateY(-1rem);
  }
}

@keyframes chat-widget-collapse-downward-mobile {
  from {
    width: calc(100vw - 2rem);
    height: calc(100vh - 8rem);
    border-radius: 1rem;
    transform: scale(1) translateY(calc(-50vh + 2rem));
  }
  to {
    width: 6rem;
    height: 4.5rem;
    border-radius: 2rem 2rem 1.5rem 1.5rem;
    transform: scale(1) translateY(-0.75rem);
  }
}

@keyframes chat-widget-merge {
  from {
    width: 8rem;
    height: 6rem;
    border-radius: 2.5rem 2.5rem 2rem 2rem;
    transform: scale(1) translateY(-1rem);
  }
  to {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    transform: scale(1) translateY(0);
  }
}

@keyframes chat-widget-merge-mobile {
  from {
    width: 6rem;
    height: 4.5rem;
    border-radius: 2rem 2rem 1.5rem 1.5rem;
    transform: scale(1) translateY(-0.75rem);
  }
  to {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    transform: scale(1) translateY(0);
  }
}

@keyframes chat-icon-hide {
  from {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  to {
    opacity: 0;
    transform: scale(0.8) rotate(90deg);
  }
}

@keyframes chat-icon-show {
  from {
    opacity: 0;
    transform: scale(0.8) rotate(-90deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes chat-content-enter {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes chat-content-leave {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* CSS Classes for Three-Step Widget Animation */

/* Step 1 -> Step 2: Split animation */
.chat-widget-splitting {
  animation: chat-widget-split 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

.chat-widget-splitting-mobile {
  animation: chat-widget-split-mobile 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

/* Step 2 -> Step 3: Expand upward animation */
.chat-widget-expanding-upward {
  animation: chat-widget-expand-upward 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

.chat-widget-expanding-upward-mobile {
  animation: chat-widget-expand-upward-mobile 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

/* Reverse animations for closing */
.chat-widget-collapsing-downward {
  animation: chat-widget-collapse-downward 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

.chat-widget-collapsing-downward-mobile {
  animation: chat-widget-collapse-downward-mobile 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

.chat-widget-merging {
  animation: chat-widget-merge 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

.chat-widget-merging-mobile {
  animation: chat-widget-merge-mobile 0.5s cubic-bezier(0.4, 0, 0.2, 1) forwards !important;
  width: auto !important;
  height: auto !important;
  transform: none !important;
}

.chat-icon-hiding {
  animation: chat-icon-hide 0.3s ease-out forwards;
}

.chat-icon-showing {
  animation: chat-icon-show 0.3s ease-out forwards;
}

.chat-content-entering {
  animation: chat-content-enter 0.3s ease-out 0.1s forwards;
  opacity: 0;
}

.chat-content-leaving {
  animation: chat-content-leave 0.3s ease-out forwards;
}

/* Mobile connector styling */
.mobile-connector {
  position: absolute;
  z-index: 0;
}

/* Mobile approach blocks styling */
@media (max-width: 767px) {
  .mobile-connector {
    left: 50% !important;
    transform: translateX(-50%);
  }

  .approach-block {
    border-radius: 16px; /* Smoothed edges but more rectangular */
    padding: 24px 20px;
    min-height: 180px; /* Ensure consistent height for rectangular appearance */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 90%;
    height: auto;
    aspect-ratio: 3 / 2; /* More rectangular shape */
  }

  /* Adjust the flex layout for mobile */
  .approach-block > div:first-child {
    margin-bottom: 16px;
  }
}