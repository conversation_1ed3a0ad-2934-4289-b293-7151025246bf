/* ...existing code... */

/* Responsive mobile view for process flow */
@media (max-width: 767px) {
  .process-block {
    width: 100%;
    margin-bottom: 10px;
  }

  .pulsing-arrow {
    margin: 5px 0 15px;
  }

  /* No mobile connections */
}

/* Ensure smooth transitions */
.process-flow * {
  transition: all 0.3s ease-in-out;
}

/* No mobile connections for approach page */

/* Animation for wiggly line */
@keyframes dash {
  to {
    stroke-dashoffset: 100;
  }
}

/* Kraken-style Chatbot Widget Animations */
@keyframes chat-widget-expand {
  from {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    transform: scale(1);
  }
  to {
    width: 384px;
    height: 600px;
    border-radius: 1rem;
    transform: scale(1);
  }
}

@keyframes chat-widget-expand-mobile {
  from {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    transform: scale(1);
  }
  to {
    width: calc(100vw - 2rem);
    height: calc(100vh - 8rem);
    border-radius: 1rem;
    transform: scale(1);
  }
}

@keyframes chat-widget-collapse {
  from {
    width: 384px;
    height: 600px;
    border-radius: 1rem;
    transform: scale(1);
  }
  to {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    transform: scale(1);
  }
}

@keyframes chat-widget-collapse-mobile {
  from {
    width: calc(100vw - 2rem);
    height: calc(100vh - 8rem);
    border-radius: 1rem;
    transform: scale(1);
  }
  to {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    transform: scale(1);
  }
}

@keyframes chat-icon-hide {
  from {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
  to {
    opacity: 0;
    transform: scale(0.8) rotate(90deg);
  }
}

@keyframes chat-icon-show {
  from {
    opacity: 0;
    transform: scale(0.8) rotate(-90deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes chat-content-enter {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes chat-content-leave {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

/* CSS Classes for Kraken-style Widget */
.chat-widget-expanding {
  animation: chat-widget-expand 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-widget-expanding-mobile {
  animation: chat-widget-expand-mobile 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-widget-collapsing {
  animation: chat-widget-collapse 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-widget-collapsing-mobile {
  animation: chat-widget-collapse-mobile 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.chat-icon-hiding {
  animation: chat-icon-hide 0.3s ease-out forwards;
}

.chat-icon-showing {
  animation: chat-icon-show 0.3s ease-out forwards;
}

.chat-content-entering {
  animation: chat-content-enter 0.3s ease-out 0.1s forwards;
  opacity: 0;
}

.chat-content-leaving {
  animation: chat-content-leave 0.3s ease-out forwards;
}

/* Mobile connector styling */
.mobile-connector {
  position: absolute;
  z-index: 0;
}

/* Mobile approach blocks styling */
@media (max-width: 767px) {
  .mobile-connector {
    left: 50% !important;
    transform: translateX(-50%);
  }

  .approach-block {
    border-radius: 16px; /* Smoothed edges but more rectangular */
    padding: 24px 20px;
    min-height: 180px; /* Ensure consistent height for rectangular appearance */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-width: 90%;
    height: auto;
    aspect-ratio: 3 / 2; /* More rectangular shape */
  }

  /* Adjust the flex layout for mobile */
  .approach-block > div:first-child {
    margin-bottom: 16px;
  }
}