// Admin page for managing the knowledge base
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

interface KnowledgeChunk {
  id: string;
  content: string;
  source: string;
  url?: string;
  timestamp: string;
  wordCount: number;
}

interface KnowledgeStats {
  totalChunks: number;
  lastUpdated: string;
  sources: string[];
  chunksByType: {
    webpage: number;
    static: number;
    faq: number;
  };
  chunks?: KnowledgeChunk[];
}

export default function KnowledgeAdmin() {
  const [stats, setStats] = useState<KnowledgeStats | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [message, setMessage] = useState('');
  const [showChunks, setShowChunks] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSource, setSelectedSource] = useState('all');

  const fetchStats = async (includeChunks: boolean = false) => {
    try {
      const url = includeChunks ? '/api/knowledge-stats?includeChunks=true' : '/api/knowledge-stats';
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Failed to fetch stats:', error);
    }
  };

  const loadChunks = async () => {
    setShowChunks(true);
    await fetchStats(true);
  };

  const refreshKnowledge = async (force: boolean = false) => {
    setIsRefreshing(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/refresh-knowledge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ force })
      });

      const data = await response.json();
      
      if (data.success) {
        setMessage(`✅ ${data.message}`);
        setStats(data.stats);
      } else {
        setMessage(`❌ Error: ${data.error}`);
      }
    } catch (error) {
      setMessage(`❌ Failed to refresh: ${error}`);
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-pink-800 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
          <h1 className="text-3xl font-bold text-white mb-8">Knowledge Base Admin</h1>
          
          {/* Stats Section */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">Total Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.totalChunks}</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">Webpage Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.chunksByType.webpage}</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">Static Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.chunksByType.static}</p>
              </div>
              <div className="bg-white/20 rounded-lg p-4">
                <h3 className="text-white/80 text-sm font-medium">FAQ Chunks</h3>
                <p className="text-2xl font-bold text-white">{stats.chunksByType.faq}</p>
              </div>
            </div>
          )}

          {/* Last Updated */}
          {stats && (
            <div className="mb-8">
              <h3 className="text-white/80 text-sm font-medium mb-2">Last Updated</h3>
              <p className="text-white">{new Date(stats.lastUpdated).toLocaleString()}</p>
            </div>
          )}

          {/* Sources */}
          {stats && stats.sources.length > 0 && (
            <div className="mb-8">
              <h3 className="text-white/80 text-sm font-medium mb-2">Sources</h3>
              <ul className="text-white space-y-1">
                {stats.sources.map((source, index) => (
                  <li key={index} className="text-sm">• {source}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Actions */}
          <div className="space-y-4">
            <div className="flex gap-4">
              <Button
                onClick={() => refreshKnowledge(false)}
                disabled={isRefreshing}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isRefreshing ? 'Refreshing...' : 'Refresh Knowledge Base'}
              </Button>
              
              <Button
                onClick={() => refreshKnowledge(true)}
                disabled={isRefreshing}
                variant="destructive"
              >
                {isRefreshing ? 'Force Refreshing...' : 'Force Refresh (Ignore Cache)'}
              </Button>
              
              <Button
                onClick={() => fetchStats()}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                Reload Stats
              </Button>

              <Button
                onClick={loadChunks}
                className="bg-green-600 hover:bg-green-700"
              >
                {showChunks ? 'Refresh Chunks' : 'View Knowledge Chunks'}
              </Button>
            </div>

            {message && (
              <div className="bg-white/20 rounded-lg p-4">
                <p className="text-white">{message}</p>
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-8 bg-white/10 rounded-lg p-4">
            <h3 className="text-white font-medium mb-2">Instructions</h3>
            <ul className="text-white/80 text-sm space-y-1">
              <li>• <strong>Refresh Knowledge Base:</strong> Updates if cache is expired (24h)</li>
              <li>• <strong>Force Refresh:</strong> Ignores cache and re-scrapes everything</li>
              <li>• <strong>Cache Location:</strong> public/knowledge-cache.json</li>
              <li>• <strong>Auto-refresh:</strong> Happens automatically when cache expires</li>
              <li>• <strong>View Knowledge Chunks:</strong> Browse all scraped content like Voiceflow</li>
            </ul>
          </div>

          {/* Knowledge Chunks Viewer */}
          {showChunks && stats?.chunks && (
            <div className="mt-8 bg-white/10 rounded-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-white font-medium text-xl">Knowledge Chunks</h3>
                <Badge className="bg-blue-600 text-white">
                  {stats.chunks.length} chunks
                </Badge>
              </div>

              {/* Search and Filter */}
              <div className="flex gap-4 mb-6">
                <Input
                  placeholder="Search chunks..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 bg-white/20 border-white/30 text-white placeholder:text-white/60"
                />
                <select
                  value={selectedSource}
                  onChange={(e) => setSelectedSource(e.target.value)}
                  className="bg-white/20 border border-white/30 rounded-md px-3 py-2 text-white"
                >
                  <option value="all">All Sources</option>
                  {stats.sources.map((source) => (
                    <option key={source} value={source}>{source}</option>
                  ))}
                </select>
              </div>

              {/* Filtered Chunks Display */}
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {stats.chunks
                  .filter(chunk => {
                    const matchesSearch = searchTerm === '' ||
                      chunk.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      chunk.source.toLowerCase().includes(searchTerm.toLowerCase());
                    const matchesSource = selectedSource === 'all' || chunk.source === selectedSource;
                    return matchesSearch && matchesSource;
                  })
                  .map((chunk, index) => (
                    <div key={chunk.id || index} className="bg-white/20 rounded-lg p-4 border border-white/30">
                      <div className="flex justify-between items-start mb-2">
                        <Badge className="bg-purple-600 text-white text-xs">
                          {chunk.source}
                        </Badge>
                        <span className="text-white/60 text-xs">
                          {chunk.wordCount} words
                        </span>
                      </div>

                      {chunk.url && (
                        <div className="mb-2">
                          <a
                            href={chunk.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-300 hover:text-blue-200 text-xs underline"
                          >
                            {chunk.url}
                          </a>
                        </div>
                      )}

                      <div className="text-white/90 text-sm leading-relaxed">
                        {chunk.content.length > 300
                          ? `${chunk.content.substring(0, 300)}...`
                          : chunk.content
                        }
                      </div>

                      <div className="mt-2 text-white/50 text-xs">
                        {new Date(chunk.timestamp).toLocaleString()}
                      </div>
                    </div>
                  ))
                }
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
