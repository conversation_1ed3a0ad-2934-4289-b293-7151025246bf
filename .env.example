# Supabase Configuration for Chatbot
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_KEY=your_supabase_anon_key
NEXT_PUBLIC_SUPABASE_TABLE=leads
NEXT_PUBLIC_SUPABASE_SUPPORT_TABLE=support_tickets

# OpenAI Configuration for AI Responses
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key

# Chatbot Configuration (optional - for iframe integration if needed)
NEXT_PUBLIC_CHATBOT_URL=http://localhost:3000
